const mongoose = require('mongoose');

const otpSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    index: true
  },
  otp: {
    type: String,
    required: true
  },
  hashedOtp: {
    type: String,
    required: true
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  purpose: {
    type: String,
    required: true,
    enum: ['login', 'registration', 'password-reset', 'transaction']
  },
  deviceFingerprint: {
    deviceId: String,
    deviceModel: String,
    systemVersion: String,
    appVersion: String,
    timestamp: Number
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: String,
  attempts: {
    type: Number,
    default: 0,
    max: 5
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  isBlocked: {
    type: Boolean,
    default: false
  },
  expiresAt: {
    type: Date,
    required: true,
    index: { expireAfterSeconds: 0 }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastAttemptAt: {
    type: Date
  }
});

// Compound indexes for better query performance
otpSchema.index({ userId: 1, purpose: 1 });
otpSchema.index({ sessionId: 1, isUsed: 1 });
otpSchema.index({ ipAddress: 1, createdAt: 1 });

// Pre-save middleware to set expiration
otpSchema.pre('save', function(next) {
  if (this.isNew) {
    // Set expiration to 60 seconds from now
    this.expiresAt = new Date(Date.now() + 60 * 1000);
  }
  next();
});

// Instance methods
otpSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

otpSchema.methods.incrementAttempts = function() {
  this.attempts += 1;
  this.lastAttemptAt = new Date();
  
  if (this.attempts >= 5) {
    this.isBlocked = true;
  }
  
  return this.save();
};

otpSchema.methods.markAsUsed = function() {
  this.isUsed = true;
  return this.save();
};

// Static methods
otpSchema.statics.findValidOTP = function(sessionId, userId) {
  return this.findOne({
    sessionId,
    userId,
    isUsed: false,
    isBlocked: false,
    expiresAt: { $gt: new Date() }
  });
};

otpSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
};

module.exports = mongoose.model('OTP', otpSchema);
