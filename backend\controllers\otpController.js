const OTPService = require('../services/otpService');
const { body, validationResult } = require('express-validator');

class OTPController {
  // Validation middleware
  static validateGenerateOTP = [
    body('userId').notEmpty().withMessage('User ID is required'),
    body('purpose').isIn(['login', 'registration', 'password-reset', 'transaction']).withMessage('Invalid purpose'),
    body('otpLength').optional().isInt({ min: 4, max: 8 }).withMessage('OTP length must be between 4 and 8'),
    body('deviceFingerprint').optional().isObject().withMessage('Device fingerprint must be an object'),
  ];

  static validateVerifyOTP = [
    body('userId').notEmpty().withMessage('User ID is required'),
    body('otp').isLength({ min: 4, max: 8 }).withMessage('OTP must be between 4 and 8 characters'),
    body('sessionId').isUUID().withMessage('Invalid session ID'),
    body('purpose').isIn(['login', 'registration', 'password-reset', 'transaction']).withMessage('Invalid purpose'),
    body('deviceFingerprint').optional().isObject().withMessage('Device fingerprint must be an object'),
  ];

  static validateRateLimit = [
    body('identifier').notEmpty().withMessage('Identifier is required'),
    body('deviceFingerprint').optional().isObject().withMessage('Device fingerprint must be an object'),
  ];

  // Generate in-app OTP
  static async generateInAppOTP(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId, purpose, deviceFingerprint, otpLength = 6 } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      // Additional security checks
      if (!ipAddress) {
        return res.status(400).json({
          success: false,
          message: 'Unable to determine client IP address'
        });
      }

      const result = await OTPService.generateInAppOTP(
        userId,
        purpose,
        deviceFingerprint,
        ipAddress,
        userAgent,
        otpLength
      );

      // Log the generation (without sensitive data)
      console.log(`OTP generated for user ${userId}, purpose: ${purpose}, IP: ${ipAddress}`);

      res.status(200).json(result);

    } catch (error) {
      console.error('Error in generateInAppOTP:', error);
      
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to generate OTP',
        code: 'OTP_GENERATION_FAILED'
      });
    }
  }

  // Verify in-app OTP
  static async verifyInAppOTP(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId, otp, sessionId, purpose, deviceFingerprint } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      const result = await OTPService.verifyInAppOTP(
        userId,
        otp,
        sessionId,
        purpose,
        deviceFingerprint,
        ipAddress
      );

      // Log successful verification (without sensitive data)
      console.log(`OTP verified successfully for user ${userId}, purpose: ${purpose}, IP: ${ipAddress}`);

      res.status(200).json(result);

    } catch (error) {
      console.error('Error in verifyInAppOTP:', error);
      
      // Don't expose too much information about why verification failed
      const safeMessage = error.message.includes('Rate limit') ? 
        error.message : 
        'OTP verification failed';

      res.status(400).json({
        success: false,
        message: safeMessage,
        code: 'OTP_VERIFICATION_FAILED'
      });
    }
  }

  // Check rate limit status
  static async checkRateLimit(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { identifier, deviceFingerprint } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      const result = await OTPService.checkUserRateLimit(
        identifier,
        ipAddress,
        deviceFingerprint
      );

      res.status(200).json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Error in checkRateLimit:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to check rate limit',
        code: 'RATE_LIMIT_CHECK_FAILED'
      });
    }
  }

  // Admin endpoint to cleanup expired records
  static async cleanup(req, res) {
    try {
      // Add authentication/authorization check here
      // if (!req.user || !req.user.isAdmin) {
      //   return res.status(403).json({ success: false, message: 'Unauthorized' });
      // }

      const result = await OTPService.cleanup();

      res.status(200).json({
        success: true,
        message: 'Cleanup completed successfully',
        data: result
      });

    } catch (error) {
      console.error('Error in cleanup:', error);
      
      res.status(500).json({
        success: false,
        message: 'Cleanup failed',
        code: 'CLEANUP_FAILED'
      });
    }
  }

  // Health check endpoint
  static async healthCheck(req, res) {
    try {
      // Basic health check - you can add more sophisticated checks
      res.status(200).json({
        success: true,
        message: 'OTP service is healthy',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'OTP service is unhealthy'
      });
    }
  }
}

module.exports = OTPController;
