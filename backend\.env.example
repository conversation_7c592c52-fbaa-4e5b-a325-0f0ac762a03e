# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/quickload_otp

# Security Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
BCRYPT_ROUNDS=12

# Rate Limiting Configuration
GLOBAL_RATE_LIMIT_WINDOW_MS=900000
GLOBAL_RATE_LIMIT_MAX=100
OTP_RATE_LIMIT_WINDOW_MS=900000
OTP_RATE_LIMIT_MAX=10
VERIFY_RATE_LIMIT_WINDOW_MS=300000
VERIFY_RATE_LIMIT_MAX=5

# OTP Configuration
DEFAULT_OTP_LENGTH=6
DEFAULT_OTP_EXPIRY_SECONDS=60
MAX_OTP_ATTEMPTS=3
OTP_CLEANUP_INTERVAL_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,https://quickloadbe.cogweel.com

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Redis Configuration (Optional - for distributed rate limiting)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Email Configuration (Optional - for backup notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring Configuration (Optional)
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_new_relic_key_here
