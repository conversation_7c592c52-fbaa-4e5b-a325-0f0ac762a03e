import axiosInstance from './axiosInstance';
import axios from 'axios';

// Create separate instance for OTP service (for testing)
const otpAxiosInstance = axios.create({
  baseURL: 'http://localhost:5000/api', // Your separate OTP backend
  withCredentials: true,
});
import DeviceInfo from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';

class OTPServices {
  // Generate device fingerprint for additional security
  static async generateDeviceFingerprint() {
    try {
      const deviceId = await DeviceInfo.getUniqueId();
      const deviceModel = await DeviceInfo.getModel();
      const systemVersion = await DeviceInfo.getSystemVersion();
      const appVersion = await DeviceInfo.getVersion();

      return {
        deviceId,
        deviceModel,
        systemVersion,
        appVersion,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      return null;
    }
  }

  // Generate secure in-app OTP
  static async generateInAppOTP(data) {
    try {
      const deviceFingerprint = await this.generateDeviceFingerprint();
      const requestData = {
        ...data,
        deviceFingerprint,
        requestType: 'in-app-otp'
      };

      const response = await otpAxiosInstance.post('/otp/generate-in-app', requestData);
      return response.data;
    } catch (error) {
      console.error('Error generating in-app OTP:', error);
      throw error;
    }
  }

  // Verify in-app OTP
  static async verifyInAppOTP(data) {
    try {
      const deviceFingerprint = await this.generateDeviceFingerprint();
      const sessionId = await AsyncStorage.getItem('otpSessionId');

      const requestData = {
        ...data,
        deviceFingerprint,
        sessionId,
        requestType: 'in-app-verify'
      };

      const response = await otpAxiosInstance.post('/otp/verify-in-app', requestData);

      // Clear session after successful verification
      if (response.data.success) {
        await AsyncStorage.removeItem('otpSessionId');
      }

      return response.data;
    } catch (error) {
      console.error('Error verifying in-app OTP:', error);
      throw error;
    }
  }

  // Check rate limiting status
  static async checkRateLimit(identifier) {
    try {
      const deviceFingerprint = await this.generateDeviceFingerprint();
      const response = await otpAxiosInstance.post('/otp/check-rate-limit', {
        identifier,
        deviceFingerprint
      });
      return response.data;
    } catch (error) {
      console.error('Error checking rate limit:', error);
      throw error;
    }
  }

  // Legacy methods (keeping for backward compatibility)
  static async sendOTP(data) {
    try {
      const response = await axiosInstance.post('send-otp', data);
      return response.data;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  }

  static async verifyOTP(data) {
    try {
      const response = await axiosInstance.post('verify-otp', data);
      return response.data;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  }

  static async forgotPassword(data) {
    try {
      const response = await axiosInstance.post('reset-password', data);
      return response.data;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  }

  static async checkPhoneExists(data) {
    try {
      const response = await axiosInstance.post('check-phone-number', data);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

export default OTPServices;
