import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  AppState,
  Platform,
} from 'react-native';
import { OtpInput } from 'react-native-otp-entry';
import AsyncStorage from '@react-native-async-storage/async-storage';
import OTPServices from '../api/otpServices';
import CustomButton from './CustomButton';
import { useTheme } from 'react-native-paper';

interface SecureInAppOTPProps {
  userId: string;
  onVerificationSuccess: (data: any) => void;
  onVerificationError: (error: string) => void;
  purpose: 'login' | 'registration' | 'password-reset' | 'transaction';
  maxAttempts?: number;
  otpLength?: number;
}

const SecureInAppOTP: React.FC<SecureInAppOTPProps> = ({
  userId,
  onVerificationSuccess,
  onVerificationError,
  purpose,
  maxAttempts = 3,
  otpLength = 6,
}) => {
  const theme = useTheme();
  const [otp, setOtp] = useState('');
  const [displayOtp, setDisplayOtp] = useState('');
  const [timeLeft, setTimeLeft] = useState(60);
  const [isLoading, setIsLoading] = useState(false);
  const [canResend, setCanResend] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [sessionId, setSessionId] = useState('');
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);

  useEffect(() => {
    generateOTP();
    startTimer();
    
    // Monitor app state changes for security
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      subscription?.remove();
    };
  }, []);

  const handleAppStateChange = (nextAppState: string) => {
    if (
      appStateRef.current.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      // App came to foreground - check if OTP is still valid
      checkOTPValidity();
    }
    appStateRef.current = nextAppState;
  };

  const checkOTPValidity = async () => {
    try {
      const storedSessionId = await AsyncStorage.getItem('otpSessionId');
      if (!storedSessionId || storedSessionId !== sessionId) {
        // Session invalid, regenerate OTP
        generateOTP();
      }
    } catch (error) {
      console.error('Error checking OTP validity:', error);
    }
  };

  const generateOTP = async () => {
    try {
      setIsLoading(true);
      
      // Check rate limiting first
      const rateLimitCheck = await OTPServices.checkRateLimit(userId);
      if (rateLimitCheck.isBlocked) {
        setIsBlocked(true);
        onVerificationError(`Too many attempts. Please try again in ${rateLimitCheck.blockTimeRemaining} minutes.`);
        return;
      }

      const response = await OTPServices.generateInAppOTP({
        userId,
        purpose,
        otpLength,
      });

      if (response.success) {
        setDisplayOtp(response.otp); // This will be displayed to user
        setSessionId(response.sessionId);
        await AsyncStorage.setItem('otpSessionId', response.sessionId);
        
        // Reset timer and attempts
        setTimeLeft(60);
        setAttempts(0);
        setCanResend(false);
        startTimer();
      } else {
        onVerificationError(response.message || 'Failed to generate OTP');
      }
    } catch (error: any) {
      onVerificationError(error.message || 'Failed to generate OTP');
    } finally {
      setIsLoading(false);
    }
  };

  const startTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          setDisplayOtp(''); // Clear displayed OTP when expired
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const verifyOTP = async () => {
    if (otp.length !== otpLength) {
      Alert.alert('Error', `Please enter ${otpLength} digit OTP`);
      return;
    }

    if (attempts >= maxAttempts) {
      setIsBlocked(true);
      onVerificationError('Maximum attempts exceeded. Please try again later.');
      return;
    }

    try {
      setIsLoading(true);
      
      const response = await OTPServices.verifyInAppOTP({
        userId,
        otp,
        sessionId,
        purpose,
      });

      if (response.success) {
        // Clear sensitive data
        setOtp('');
        setDisplayOtp('');
        await AsyncStorage.removeItem('otpSessionId');
        
        onVerificationSuccess(response.data);
      } else {
        setAttempts(prev => prev + 1);
        setOtp(''); // Clear entered OTP
        onVerificationError(response.message || 'Invalid OTP');
        
        if (attempts + 1 >= maxAttempts) {
          setIsBlocked(true);
        }
      }
    } catch (error: any) {
      setAttempts(prev => prev + 1);
      setOtp('');
      onVerificationError(error.message || 'Verification failed');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isBlocked) {
    return (
      <View style={styles.container}>
        <Text style={[styles.title, { color: theme.colors.error }]}>
          Account Temporarily Blocked
        </Text>
        <Text style={styles.description}>
          Too many failed attempts. Please try again later.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Secure Verification
      </Text>
      
      <Text style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
        Enter the OTP displayed below to verify your identity
      </Text>

      {/* Display OTP for user to see */}
      <View style={[styles.otpDisplay, { backgroundColor: theme.colors.surfaceVariant }]}>
        <Text style={[styles.otpText, { color: theme.colors.primary }]}>
          {displayOtp || 'OTP Expired'}
        </Text>
        <Text style={[styles.timerText, { color: theme.colors.onSurfaceVariant }]}>
          {timeLeft > 0 ? `Expires in: ${formatTime(timeLeft)}` : 'Expired'}
        </Text>
      </View>

      {/* OTP Input */}
      <OtpInput
        numberOfDigits={otpLength}
        onTextChange={setOtp}
        onFilled={verifyOTP}
        theme={{
          containerStyle: styles.otpContainer,
          pinCodeContainerStyle: [
            styles.otpInput,
            { borderColor: theme.colors.outline }
          ],
          pinCodeTextStyle: [
            styles.otpInputText,
            { color: theme.colors.onSurface }
          ],
          focusedPinCodeContainerStyle: {
            borderColor: theme.colors.primary,
          },
        }}
      />

      <Text style={[styles.attemptsText, { color: theme.colors.onSurfaceVariant }]}>
        Attempts remaining: {maxAttempts - attempts}
      </Text>

      <CustomButton
        title="Verify OTP"
        onPress={verifyOTP}
        loading={isLoading}
        disabled={otp.length !== otpLength || timeLeft === 0}
        style={styles.verifyButton}
      />

      <CustomButton
        title="Generate New OTP"
        onPress={generateOTP}
        loading={isLoading}
        disabled={!canResend}
        variant="outlined"
        style={styles.resendButton}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  otpDisplay: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 30,
    alignItems: 'center',
    minWidth: 200,
  },
  otpText: {
    fontSize: 32,
    fontWeight: 'bold',
    letterSpacing: 8,
    marginBottom: 8,
  },
  timerText: {
    fontSize: 14,
    fontWeight: '500',
  },
  otpContainer: {
    marginBottom: 20,
  },
  otpInput: {
    borderWidth: 2,
    borderRadius: 8,
    width: 45,
    height: 55,
  },
  otpInputText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  attemptsText: {
    fontSize: 14,
    marginBottom: 20,
  },
  verifyButton: {
    marginBottom: 15,
    width: '100%',
  },
  resendButton: {
    width: '100%',
  },
});

export default SecureInAppOTP;
