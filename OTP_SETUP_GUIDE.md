# 🚀 Quick Setup Guide for Secure In-App OTP System

## **Choose Your Setup Option:**

### **Option 1: Integrate into Existing Backend (Recommended)**

If you have access to your existing backend at `https://quickloadbe.cogweel.com/`:

#### **Step 1: Copy Backend Files**
Move these files to your existing backend:

```bash
# Copy to your existing backend project
cp backend/models/OTP.js /path/to/your/backend/models/
cp backend/models/RateLimit.js /path/to/your/backend/models/
cp backend/services/otpService.js /path/to/your/backend/services/
cp backend/controllers/otpController.js /path/to/your/backend/controllers/
cp backend/routes/otpRoutes.js /path/to/your/backend/routes/
```

#### **Step 2: Install Dependencies**
Add to your existing backend's package.json:

```bash
npm install bcrypt express-rate-limit express-mongo-sanitize express-validator xss-clean hpp uuid
```

#### **Step 3: Update Your Main App File**
Add to your existing Express app:

```javascript
// In your main app.js or server.js
const otpRoutes = require('./routes/otpRoutes');
app.use('/api/otp', otpRoutes);
```

#### **Step 4: Update Frontend Config**
Change the OTP service to use your existing backend:

```javascript
// In src/api/otpServices.js, replace the otpAxiosInstance with:
const otpAxiosInstance = axiosInstance; // Use your existing axios instance
```

---

### **Option 2: Separate OTP Backend (For Testing)**

If you want to test separately first:

#### **Step 1: Set Up Separate Backend**

```bash
# Navigate to backend folder
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your MongoDB URI

# Start the OTP backend
npm run dev
```

#### **Step 2: Frontend is Already Configured**
The frontend is already set up to use `http://localhost:5000/api` for OTP endpoints.

---

## **Frontend Setup (Both Options)**

### **Step 1: Install Required Dependencies**

```bash
# In your React Native project root
npm install react-native-device-info

# For iOS (if you're building for iOS)
cd ios && pod install && cd ..
```

### **Step 2: Add OTP Screen to Navigation**

Add to your auth navigation:

```javascript
// In src/navigation/auth/index.js
import SecureOTPScreen from '../../screens/auth/SecureOTPScreen';

// Add to your Stack.Navigator
<Stack.Screen 
  name="SecureOTP" 
  component={SecureOTPScreen}
  options={{ title: 'Secure Verification' }}
/>
```

### **Step 3: Use the OTP Component**

```javascript
// Example usage in any screen
import SecureInAppOTP from '../components/SecureInAppOTP';

const MyScreen = () => {
  const handleSuccess = (data) => {
    console.log('OTP verified:', data);
    // Navigate to next screen
  };

  const handleError = (error) => {
    Alert.alert('Error', error);
  };

  return (
    <SecureInAppOTP
      userId="user123"
      purpose="login"
      onVerificationSuccess={handleSuccess}
      onVerificationError={handleError}
    />
  );
};
```

---

## **Testing the System**

### **Test Backend (Option 2 only)**

```bash
# Test OTP generation
curl -X POST http://localhost:5000/api/otp/generate-in-app \
  -H "Content-Type: application/json" \
  -d '{"userId":"test123","purpose":"login"}'

# Test OTP verification (use OTP from above response)
curl -X POST http://localhost:5000/api/otp/verify-in-app \
  -H "Content-Type: application/json" \
  -d '{"userId":"test123","otp":"123456","sessionId":"session-id","purpose":"login"}'
```

### **Test Frontend**

1. **Navigate to OTP screen:**
   ```javascript
   navigation.navigate('SecureOTP', {
     initialUserId: 'test123',
     initialPurpose: 'login',
     autoStart: true
   });
   ```

2. **Check the logs** for any errors
3. **Verify OTP display** and input functionality

---

## **Environment Configuration**

### **Backend Environment (.env)**

```env
# Database
MONGODB_URI=mongodb://localhost:27017/quickload_otp

# Security
JWT_SECRET=your_super_secret_jwt_key_here
BCRYPT_ROUNDS=12

# Server
PORT=5000
NODE_ENV=development

# CORS (add your frontend URL)
ALLOWED_ORIGINS=http://localhost:8081,https://quickloadbe.cogweel.com
```

### **React Native Environment**

Make sure your `src/api/apiConfig.js` includes the OTP backend URL if using Option 2.

---

## **Production Deployment**

### **For Option 1 (Integrated Backend):**
- Deploy as part of your existing backend
- Update CORS settings for production domains
- Set production MongoDB URI
- Use strong JWT secrets

### **For Option 2 (Separate Backend):**
- Deploy OTP backend separately (e.g., on different port/subdomain)
- Update frontend to point to production OTP backend
- Set up reverse proxy if needed

---

## **Security Checklist**

✅ **MongoDB connection secured**  
✅ **Strong JWT secrets set**  
✅ **CORS properly configured**  
✅ **Rate limiting enabled**  
✅ **Input validation active**  
✅ **HTTPS in production**  
✅ **Environment variables secured**  

---

## **Troubleshooting**

### **Common Issues:**

1. **"Device fingerprint failed"**
   - Ensure `react-native-device-info` is properly installed
   - Run `cd ios && pod install` for iOS

2. **"Rate limit exceeded"**
   - Wait for rate limit window to reset
   - Check rate limit configuration

3. **"MongoDB connection failed"**
   - Verify MongoDB is running
   - Check connection string in .env

4. **"CORS error"**
   - Add your frontend URL to ALLOWED_ORIGINS
   - Check CORS configuration

### **Debug Mode:**

Enable debug logging in backend:

```javascript
// In backend app.js
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`, req.body);
    next();
  });
}
```

---

## **Next Steps**

1. **Choose your setup option** (1 or 2)
2. **Follow the setup steps** for your chosen option
3. **Test the system** with the provided examples
4. **Integrate into your app** authentication flow
5. **Deploy to production** when ready

**Need help?** Check the main `SECURE_OTP_SYSTEM.md` for detailed documentation and security best practices.
