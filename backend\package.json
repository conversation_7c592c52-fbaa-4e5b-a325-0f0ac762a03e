{"name": "quickload-otp-backend", "version": "1.0.0", "description": "Secure in-app OTP system for QuickLoad", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcrypt": "^5.1.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-mongo-sanitize": "^2.2.0", "express-validator": "^7.0.1", "xss-clean": "^0.1.4", "hpp": "^0.2.3", "uuid": "^9.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["otp", "security", "authentication", "express", "mongodb", "rate-limiting"], "author": "QuickLoad Team", "license": "MIT"}