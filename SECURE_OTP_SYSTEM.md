# Secure In-App OTP System for QuickLoad

## Overview

This is a comprehensive secure OTP (One-Time Password) system designed specifically for users who are not familiar with email or SMS. The system generates and verifies OTPs entirely within the app, providing a secure authentication mechanism without requiring external communication channels.

## 🔒 Security Features

### Core Security

- **In-app OTP generation**: No external SMS/email required
- **Device fingerprinting**: Binds OTP to specific device characteristics
- **Session binding**: Each OTP is tied to a unique session
- **Short expiry**: 60-second expiration time
- **One-time use**: Each OTP can only be used once
- **Secure hashing**: OTPs are hashed using bcrypt with 12 salt rounds

### Rate Limiting & Protection

- **Multi-level rate limiting**: User, IP, and device-based limits
- **Progressive blocking**: Increasing block times for repeated violations
- **Attempt tracking**: Limited verification attempts per OTP
- **IP binding**: Optional IP address verification
- **CAPTCHA ready**: Architecture supports CAPTCHA integration

### Additional Security

- **XSS protection**: Input sanitization and output encoding
- **NoSQL injection protection**: MongoDB query sanitization
- **CORS configuration**: Restricted cross-origin requests
- **Helmet security headers**: Comprehensive HTTP security headers
- **Request logging**: Detailed audit trail

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native  │    │   Express.js    │    │    MongoDB      │
│   Frontend      │◄──►│   Backend       │◄──►│   Database      │
│                 │    │                 │    │                 │
│ • OTP Display   │    │ • OTP Generation│    │ • OTP Storage   │
│ • User Input    │    │ • Verification  │    │ • Rate Limits   │
│ • Device Info   │    │ • Rate Limiting │    │ • Session Data  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📱 Frontend Implementation

### Installation

1. Install required dependencies:

```bash
npm install react-native-device-info react-native-otp-entry
```

2. For iOS, add to `ios/Podfile`:

```ruby
pod 'RNDeviceInfo', :path => '../node_modules/react-native-device-info'
```

3. Run pod install:

```bash
cd ios && pod install
```

### Usage Example

```tsx
import SecureInAppOTP from '../components/SecureInAppOTP';

const MyComponent = () => {
  const handleSuccess = data => {
    console.log('OTP verified:', data);
    // Navigate to next screen or complete action
  };

  const handleError = error => {
    console.error('OTP error:', error);
    // Show error message
  };

  return (
    <SecureInAppOTP
      userId="user123"
      purpose="login"
      onVerificationSuccess={handleSuccess}
      onVerificationError={handleError}
      maxAttempts={3}
      otpLength={6}
    />
  );
};
```

### Integration with Navigation

```tsx
// Navigate to OTP screen
navigation.navigate('SecureOTP', {
  initialUserId: 'user123',
  initialPurpose: 'login',
  autoStart: true,
});
```

## 🖥️ Backend Implementation

### Installation

1. Navigate to backend directory:

```bash
cd backend
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the server:

```bash
# Development
npm run dev

# Production
npm start
```

### Environment Configuration

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/quickload_otp

# Security
JWT_SECRET=your_super_secret_jwt_key_here
BCRYPT_ROUNDS=12

# Rate Limiting
GLOBAL_RATE_LIMIT_MAX=100
OTP_RATE_LIMIT_MAX=10
VERIFY_RATE_LIMIT_MAX=5
```

### API Endpoints

#### Generate OTP

```http
POST /api/otp/generate-in-app
Content-Type: application/json

{
  "userId": "user123",
  "purpose": "login",
  "deviceFingerprint": {
    "deviceId": "unique-device-id",
    "deviceModel": "iPhone 14",
    "systemVersion": "16.0",
    "appVersion": "1.0.0"
  },
  "otpLength": 6
}
```

#### Verify OTP

```http
POST /api/otp/verify-in-app
Content-Type: application/json

{
  "userId": "user123",
  "otp": "123456",
  "sessionId": "uuid-session-id",
  "purpose": "login",
  "deviceFingerprint": { ... }
}
```

## 🛡️ Security Best Practices

### Frontend Security

1. **Never log OTPs**: Ensure OTPs are never logged or stored in plain text
2. **Clear sensitive data**: Remove OTPs from memory after use
3. **App state monitoring**: Regenerate OTP when app comes to foreground
4. **Input validation**: Validate OTP format before sending to backend
5. **Secure storage**: Use encrypted storage for session data

### Backend Security

1. **Hash OTPs**: Always hash OTPs before storing in database
2. **Rate limiting**: Implement multiple layers of rate limiting
3. **Input sanitization**: Sanitize all inputs to prevent injection attacks
4. **Audit logging**: Log all OTP operations for security monitoring
5. **Regular cleanup**: Remove expired OTPs and rate limit records

### Database Security

1. **Indexes**: Proper indexing for performance and security
2. **TTL**: Use MongoDB TTL for automatic cleanup
3. **Encryption**: Enable encryption at rest
4. **Access control**: Restrict database access
5. **Backup**: Regular encrypted backups

## 📊 Monitoring & Analytics

### Key Metrics to Monitor

- OTP generation rate
- Verification success rate
- Failed attempt patterns
- Rate limit violations
- Device fingerprint mismatches
- Session anomalies

### Logging

The system logs the following events:

- OTP generation requests
- Verification attempts
- Rate limit violations
- Security anomalies
- System errors

## 🔧 Customization

### OTP Length

```tsx
<SecureInAppOTP otpLength={8} /> // 4-8 digits supported
```

### Expiry Time

Modify in backend service:

```javascript
// In OTP model
this.expiresAt = new Date(Date.now() + 120 * 1000); // 2 minutes
```

### Rate Limits

Adjust in backend configuration:

```javascript
// User rate limit: 10 attempts per 30 minutes
RateLimit.checkLimit(`user:${userId}`, 'otp_generation', 10, 30);
```

### Device Fingerprinting

Add more device characteristics:

```javascript
const deviceFingerprint = {
  deviceId: await DeviceInfo.getUniqueId(),
  deviceModel: await DeviceInfo.getModel(),
  systemVersion: await DeviceInfo.getSystemVersion(),
  appVersion: await DeviceInfo.getVersion(),
  // Add more as needed
  screenResolution: await DeviceInfo.getScreenResolution(),
  timezone: await DeviceInfo.getTimezone(),
};
```

## 🚀 Deployment

### Frontend Deployment

1. Build the React Native app:

```bash
# Android
npx react-native run-android --variant=release

# iOS
npx react-native run-ios --configuration Release
```

### Backend Deployment

1. Set production environment variables
2. Use PM2 for process management:

```bash
npm install -g pm2
pm2 start app.js --name "otp-service"
```

3. Set up reverse proxy (nginx):

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/otp {
        proxy_pass http://localhost:5000;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🧪 Testing

### Frontend Testing

```bash
# Run component tests
npm test

# Test OTP component specifically
npm test SecureInAppOTP
```

### Backend Testing

```bash
# Run API tests
npm test

# Test specific endpoints
npm test -- --grep "OTP"
```

### Security Testing

1. **Rate limiting**: Test with multiple rapid requests
2. **Device spoofing**: Test with modified device fingerprints
3. **Session hijacking**: Test with invalid session IDs
4. **Brute force**: Test with multiple wrong OTPs
5. **Timing attacks**: Verify consistent response times

## 📞 Support

For issues or questions:

1. Check the logs for error details
2. Verify environment configuration
3. Test with curl/Postman for API issues
4. Check device permissions for React Native issues

## 🔄 Updates & Maintenance

### Regular Tasks

1. **Monitor rate limits**: Adjust based on usage patterns
2. **Update dependencies**: Keep security patches current
3. **Review logs**: Check for security anomalies
4. **Performance tuning**: Optimize database queries
5. **Backup verification**: Test backup restoration

### Security Updates

1. Rotate JWT secrets regularly
2. Update bcrypt rounds as needed
3. Review and update CORS origins
4. Monitor for new security vulnerabilities
5. Update device fingerprinting methods

This secure OTP system provides a robust, user-friendly authentication mechanism that doesn't rely on external communication channels while maintaining high security standards.

## 🎯 Quick Start Guide

### 1. Backend Setup (5 minutes)

```bash
# Clone or create backend directory
mkdir quickload-otp-backend && cd quickload-otp-backend

# Copy the provided backend files
# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your MongoDB URI

# Start server
npm run dev
```

### 2. Frontend Integration (2 minutes)

```bash
# In your React Native project
npm install react-native-device-info

# Copy the provided components
# Import and use SecureInAppOTP component
```

### 3. Test the System

```bash
# Test OTP generation
curl -X POST http://localhost:5000/api/otp/generate-in-app \
  -H "Content-Type: application/json" \
  -d '{"userId":"test123","purpose":"login"}'

# Test OTP verification (use the OTP from above response)
curl -X POST http://localhost:5000/api/otp/verify-in-app \
  -H "Content-Type: application/json" \
  -d '{"userId":"test123","otp":"123456","sessionId":"session-id","purpose":"login"}'
```

## 🔍 Troubleshooting

### Common Issues

1. **"Device fingerprint failed"**: Ensure react-native-device-info is properly installed
2. **"Rate limit exceeded"**: Wait for the rate limit window to reset or adjust limits
3. **"OTP expired"**: Generate a new OTP (60-second expiry)
4. **"Session invalid"**: Ensure sessionId matches the one from generation
5. **"MongoDB connection failed"**: Check MongoDB URI and ensure MongoDB is running

### Debug Mode

Enable debug logging in backend:

```javascript
// In app.js
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`, req.body);
    next();
  });
}
```
