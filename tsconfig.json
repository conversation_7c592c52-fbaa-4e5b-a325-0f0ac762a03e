{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios"]}