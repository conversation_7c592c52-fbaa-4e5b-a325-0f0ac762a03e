const express = require('express');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const OTPController = require('../controllers/otpController');

const router = express.Router();

// Security middleware
router.use(helmet());

// Rate limiting middleware for OTP endpoints
const otpRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    success: false,
    message: 'Too many OTP requests from this IP, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Custom key generator to include user agent
  keyGenerator: (req) => {
    return `${req.ip}-${req.get('User-Agent') || 'unknown'}`;
  }
});

// Stricter rate limiting for verification attempts
const verifyRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 5, // Limit each IP to 5 verification attempts per 5 minutes
  message: {
    success: false,
    message: 'Too many verification attempts from this IP, please try again later.',
    code: 'VERIFY_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return `${req.ip}-${req.body.userId || 'unknown'}-verify`;
  }
});

// Middleware to log requests (optional)
const logRequest = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip} - User-Agent: ${req.get('User-Agent')}`);
  next();
};

// Apply logging middleware
router.use(logRequest);

// Routes

/**
 * @route   POST /api/otp/generate-in-app
 * @desc    Generate a secure in-app OTP
 * @access  Public (with rate limiting)
 * @body    { userId, purpose, deviceFingerprint?, otpLength? }
 */
router.post('/generate-in-app', 
  otpRateLimit,
  OTPController.validateGenerateOTP,
  OTPController.generateInAppOTP
);

/**
 * @route   POST /api/otp/verify-in-app
 * @desc    Verify an in-app OTP
 * @access  Public (with rate limiting)
 * @body    { userId, otp, sessionId, purpose, deviceFingerprint? }
 */
router.post('/verify-in-app',
  verifyRateLimit,
  OTPController.validateVerifyOTP,
  OTPController.verifyInAppOTP
);

/**
 * @route   POST /api/otp/check-rate-limit
 * @desc    Check rate limit status for a user/IP
 * @access  Public
 * @body    { identifier, deviceFingerprint? }
 */
router.post('/check-rate-limit',
  OTPController.validateRateLimit,
  OTPController.checkRateLimit
);

/**
 * @route   POST /api/otp/cleanup
 * @desc    Cleanup expired OTP records (Admin only)
 * @access  Admin
 */
router.post('/cleanup',
  // Add authentication middleware here
  // authenticateToken,
  // requireAdmin,
  OTPController.cleanup
);

/**
 * @route   GET /api/otp/health
 * @desc    Health check for OTP service
 * @access  Public
 */
router.get('/health',
  OTPController.healthCheck
);

// Error handling middleware for this router
router.use((error, req, res, next) => {
  console.error('OTP Route Error:', error);
  
  res.status(500).json({
    success: false,
    message: 'Internal server error in OTP service',
    code: 'OTP_SERVICE_ERROR'
  });
});

module.exports = router;
