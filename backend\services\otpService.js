const crypto = require('crypto');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const OTP = require('../models/OTP');
const RateLimit = require('../models/RateLimit');

class OTPService {
  // Generate cryptographically secure OTP
  static generateSecureOTP(length = 6) {
    const digits = '0123456789';
    let otp = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, digits.length);
      otp += digits[randomIndex];
    }
    
    return otp;
  }

  // Hash OTP for secure storage
  static async hashOTP(otp) {
    const saltRounds = 12;
    return await bcrypt.hash(otp, saltRounds);
  }

  // Verify OTP against hash
  static async verifyOTPHash(otp, hashedOtp) {
    return await bcrypt.compare(otp, hashedOtp);
  }

  // Generate device fingerprint hash
  static generateDeviceHash(deviceFingerprint) {
    if (!deviceFingerprint) return null;
    
    const deviceString = `${deviceFingerprint.deviceId}-${deviceFingerprint.deviceModel}-${deviceFingerprint.systemVersion}`;
    return crypto.createHash('sha256').update(deviceString).digest('hex');
  }

  // Check comprehensive rate limits
  static async checkRateLimits(userId, ipAddress, deviceFingerprint) {
    const checks = await Promise.all([
      // User-based rate limiting
      RateLimit.checkLimit(`user:${userId}`, 'otp_generation', 5, 15), // 5 attempts per 15 minutes
      
      // IP-based rate limiting
      RateLimit.checkLimit(`ip:${ipAddress}`, 'otp_generation', 10, 60), // 10 attempts per hour
      
      // Device-based rate limiting (if available)
      deviceFingerprint ? 
        RateLimit.checkLimit(`device:${this.generateDeviceHash(deviceFingerprint)}`, 'otp_generation', 8, 30) :
        { canProceed: true, isBlocked: false }
    ]);

    const [userLimit, ipLimit, deviceLimit] = checks;

    // If any limit is exceeded, block the request
    if (!userLimit.canProceed || !ipLimit.canProceed || !deviceLimit.canProceed) {
      return {
        canProceed: false,
        isBlocked: true,
        reason: userLimit.isBlocked ? 'user' : ipLimit.isBlocked ? 'ip' : 'device',
        blockTimeRemaining: Math.max(
          userLimit.blockTimeRemaining || 0,
          ipLimit.blockTimeRemaining || 0,
          deviceLimit.blockTimeRemaining || 0
        )
      };
    }

    return {
      canProceed: true,
      isBlocked: false,
      limits: { userLimit, ipLimit, deviceLimit }
    };
  }

  // Generate in-app OTP
  static async generateInAppOTP(userId, purpose, deviceFingerprint, ipAddress, userAgent, otpLength = 6) {
    try {
      // Check rate limits
      const rateLimitCheck = await this.checkRateLimits(userId, ipAddress, deviceFingerprint);
      
      if (!rateLimitCheck.canProceed) {
        throw new Error(`Rate limit exceeded. Try again in ${rateLimitCheck.blockTimeRemaining} minutes.`);
      }

      // Invalidate any existing OTPs for this user and purpose
      await OTP.updateMany(
        { userId, purpose, isUsed: false },
        { isUsed: true }
      );

      // Generate new OTP
      const otp = this.generateSecureOTP(otpLength);
      const hashedOtp = await this.hashOTP(otp);
      const sessionId = uuidv4();

      // Create OTP record
      const otpRecord = new OTP({
        userId,
        otp: otp, // Store plaintext for display (will be cleared after display)
        hashedOtp,
        sessionId,
        purpose,
        deviceFingerprint,
        ipAddress,
        userAgent
      });

      await otpRecord.save();

      // Update rate limit counters
      if (rateLimitCheck.limits) {
        await Promise.all([
          rateLimitCheck.limits.userLimit.record.incrementAttempts(),
          rateLimitCheck.limits.ipLimit.record.incrementAttempts(),
          rateLimitCheck.limits.deviceLimit.record?.incrementAttempts()
        ].filter(Boolean));
      }

      // Return OTP for display (in production, consider additional security measures)
      return {
        success: true,
        otp: otp, // This will be displayed to user in-app
        sessionId,
        expiresAt: otpRecord.expiresAt,
        message: 'OTP generated successfully'
      };

    } catch (error) {
      console.error('Error generating OTP:', error);
      throw error;
    }
  }

  // Verify in-app OTP
  static async verifyInAppOTP(userId, otp, sessionId, purpose, deviceFingerprint, ipAddress) {
    try {
      // Find valid OTP record
      const otpRecord = await OTP.findValidOTP(sessionId, userId);

      if (!otpRecord) {
        throw new Error('Invalid or expired OTP session');
      }

      // Check if OTP matches the purpose
      if (otpRecord.purpose !== purpose) {
        throw new Error('OTP purpose mismatch');
      }

      // Verify device fingerprint if provided
      if (deviceFingerprint && otpRecord.deviceFingerprint) {
        const currentDeviceHash = this.generateDeviceHash(deviceFingerprint);
        const storedDeviceHash = this.generateDeviceHash(otpRecord.deviceFingerprint);
        
        if (currentDeviceHash !== storedDeviceHash) {
          await otpRecord.incrementAttempts();
          throw new Error('Device verification failed');
        }
      }

      // Verify IP address (optional - can be made configurable)
      if (otpRecord.ipAddress !== ipAddress) {
        console.warn(`IP address mismatch for OTP verification: ${otpRecord.ipAddress} vs ${ipAddress}`);
        // You can choose to block or just log this
      }

      // Verify OTP
      const isValidOTP = await this.verifyOTPHash(otp, otpRecord.hashedOtp);

      if (!isValidOTP) {
        await otpRecord.incrementAttempts();
        throw new Error('Invalid OTP');
      }

      // Mark OTP as used
      await otpRecord.markAsUsed();

      // Clear the plaintext OTP from database
      otpRecord.otp = '';
      await otpRecord.save();

      return {
        success: true,
        message: 'OTP verified successfully',
        data: {
          userId,
          purpose,
          verifiedAt: new Date(),
          sessionId
        }
      };

    } catch (error) {
      console.error('Error verifying OTP:', error);
      throw error;
    }
  }

  // Check if user/IP is rate limited
  static async checkUserRateLimit(userId, ipAddress, deviceFingerprint) {
    const rateLimitCheck = await this.checkRateLimits(userId, ipAddress, deviceFingerprint);
    
    return {
      canProceed: rateLimitCheck.canProceed,
      isBlocked: rateLimitCheck.isBlocked,
      blockTimeRemaining: rateLimitCheck.blockTimeRemaining || 0,
      reason: rateLimitCheck.reason
    };
  }

  // Cleanup expired OTPs and rate limits (run periodically)
  static async cleanup() {
    try {
      const [otpCleanup, rateLimitCleanup] = await Promise.all([
        OTP.cleanupExpired(),
        RateLimit.cleanupExpired()
      ]);

      console.log(`Cleanup completed: ${otpCleanup.deletedCount} OTPs, ${rateLimitCleanup.deletedCount} rate limits`);
      
      return {
        otpsDeleted: otpCleanup.deletedCount,
        rateLimitsDeleted: rateLimitCleanup.deletedCount
      };
    } catch (error) {
      console.error('Error during cleanup:', error);
      throw error;
    }
  }
}

module.exports = OTPService;
