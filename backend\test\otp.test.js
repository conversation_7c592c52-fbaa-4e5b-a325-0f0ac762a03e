const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../app');

// Test database
const MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/quickload_otp_test';

describe('OTP API', () => {
  let server;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    server = app.listen(0); // Use random port for testing
  });

  afterAll(async () => {
    // Clean up
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    server.close();
  });

  beforeEach(async () => {
    // Clear collections before each test
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }
  });

  describe('POST /api/otp/generate-in-app', () => {
    it('should generate OTP successfully', async () => {
      const response = await request(app)
        .post('/api/otp/generate-in-app')
        .send({
          userId: 'test123',
          purpose: 'login',
          deviceFingerprint: {
            deviceId: 'test-device-123',
            deviceModel: 'Test Device',
            systemVersion: '1.0',
            appVersion: '1.0.0'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.otp).toMatch(/^\d{6}$/); // 6-digit OTP
      expect(response.body.sessionId).toBeDefined();
      expect(response.body.expiresAt).toBeDefined();
    });

    it('should fail with invalid purpose', async () => {
      const response = await request(app)
        .post('/api/otp/generate-in-app')
        .send({
          userId: 'test123',
          purpose: 'invalid-purpose'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should fail without userId', async () => {
      const response = await request(app)
        .post('/api/otp/generate-in-app')
        .send({
          purpose: 'login'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/otp/verify-in-app', () => {
    let generatedOTP;
    let sessionId;

    beforeEach(async () => {
      // Generate OTP first
      const generateResponse = await request(app)
        .post('/api/otp/generate-in-app')
        .send({
          userId: 'test123',
          purpose: 'login',
          deviceFingerprint: {
            deviceId: 'test-device-123',
            deviceModel: 'Test Device',
            systemVersion: '1.0',
            appVersion: '1.0.0'
          }
        });

      generatedOTP = generateResponse.body.otp;
      sessionId = generateResponse.body.sessionId;
    });

    it('should verify OTP successfully', async () => {
      const response = await request(app)
        .post('/api/otp/verify-in-app')
        .send({
          userId: 'test123',
          otp: generatedOTP,
          sessionId: sessionId,
          purpose: 'login',
          deviceFingerprint: {
            deviceId: 'test-device-123',
            deviceModel: 'Test Device',
            systemVersion: '1.0',
            appVersion: '1.0.0'
          }
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.userId).toBe('test123');
      expect(response.body.data.purpose).toBe('login');
    });

    it('should fail with wrong OTP', async () => {
      const response = await request(app)
        .post('/api/otp/verify-in-app')
        .send({
          userId: 'test123',
          otp: '000000', // Wrong OTP
          sessionId: sessionId,
          purpose: 'login'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should fail with wrong sessionId', async () => {
      const response = await request(app)
        .post('/api/otp/verify-in-app')
        .send({
          userId: 'test123',
          otp: generatedOTP,
          sessionId: 'wrong-session-id',
          purpose: 'login'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should fail to verify same OTP twice', async () => {
      // First verification
      await request(app)
        .post('/api/otp/verify-in-app')
        .send({
          userId: 'test123',
          otp: generatedOTP,
          sessionId: sessionId,
          purpose: 'login'
        });

      // Second verification (should fail)
      const response = await request(app)
        .post('/api/otp/verify-in-app')
        .send({
          userId: 'test123',
          otp: generatedOTP,
          sessionId: sessionId,
          purpose: 'login'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/otp/check-rate-limit', () => {
    it('should return rate limit status', async () => {
      const response = await request(app)
        .post('/api/otp/check-rate-limit')
        .send({
          identifier: 'test123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.canProceed).toBeDefined();
      expect(response.body.data.isBlocked).toBeDefined();
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const userId = 'rate-limit-test';
      
      // Make multiple requests quickly
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/otp/generate-in-app')
            .send({
              userId: userId,
              purpose: 'login'
            })
        );
      }

      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('GET /api/otp/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/otp/health');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('OTP service is healthy');
    });
  });
});

// Helper function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
