const mongoose = require('mongoose');

const rateLimitSchema = new mongoose.Schema({
  identifier: {
    type: String,
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['otp_generation', 'otp_verification', 'ip_address', 'device']
  },
  attempts: {
    type: Number,
    default: 0
  },
  maxAttempts: {
    type: Number,
    required: true
  },
  windowStart: {
    type: Date,
    required: true
  },
  windowDuration: {
    type: Number,
    required: true // in minutes
  },
  isBlocked: {
    type: Boolean,
    default: false
  },
  blockUntil: {
    type: Date
  },
  deviceFingerprint: {
    deviceId: String,
    deviceModel: String,
    systemVersion: String,
    appVersion: String
  },
  ipAddress: String,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound indexes
rateLimitSchema.index({ identifier: 1, type: 1 });
rateLimitSchema.index({ ipAddress: 1, type: 1 });
rateLimitSchema.index({ blockUntil: 1 });

// Pre-save middleware
rateLimitSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
rateLimitSchema.methods.isInWindow = function() {
  const now = new Date();
  const windowEnd = new Date(this.windowStart.getTime() + (this.windowDuration * 60 * 1000));
  return now >= this.windowStart && now <= windowEnd;
};

rateLimitSchema.methods.isCurrentlyBlocked = function() {
  if (!this.isBlocked || !this.blockUntil) {
    return false;
  }
  return new Date() < this.blockUntil;
};

rateLimitSchema.methods.incrementAttempts = function() {
  if (!this.isInWindow()) {
    // Reset window
    this.windowStart = new Date();
    this.attempts = 1;
  } else {
    this.attempts += 1;
  }

  // Check if should be blocked
  if (this.attempts >= this.maxAttempts) {
    this.isBlocked = true;
    // Block for progressively longer periods
    const blockDuration = Math.min(this.attempts * 5, 60); // Max 60 minutes
    this.blockUntil = new Date(Date.now() + (blockDuration * 60 * 1000));
  }

  return this.save();
};

rateLimitSchema.methods.reset = function() {
  this.attempts = 0;
  this.isBlocked = false;
  this.blockUntil = null;
  this.windowStart = new Date();
  return this.save();
};

rateLimitSchema.methods.getBlockTimeRemaining = function() {
  if (!this.isCurrentlyBlocked()) {
    return 0;
  }
  return Math.ceil((this.blockUntil.getTime() - Date.now()) / (60 * 1000));
};

// Static methods
rateLimitSchema.statics.checkLimit = async function(identifier, type, maxAttempts = 5, windowDuration = 15) {
  let record = await this.findOne({ identifier, type });
  
  if (!record) {
    record = new this({
      identifier,
      type,
      maxAttempts,
      windowDuration,
      windowStart: new Date(),
      attempts: 0
    });
    await record.save();
  }

  return {
    canProceed: !record.isCurrentlyBlocked() && (record.isInWindow() ? record.attempts < record.maxAttempts : true),
    attemptsRemaining: record.maxAttempts - record.attempts,
    isBlocked: record.isCurrentlyBlocked(),
    blockTimeRemaining: record.getBlockTimeRemaining(),
    record
  };
};

rateLimitSchema.statics.cleanupExpired = function() {
  const now = new Date();
  return this.deleteMany({
    $or: [
      { blockUntil: { $lt: now } },
      { 
        isBlocked: false,
        updatedAt: { $lt: new Date(now.getTime() - 24 * 60 * 60 * 1000) } // 24 hours old
      }
    ]
  });
};

module.exports = mongoose.model('RateLimit', rateLimitSchema);
