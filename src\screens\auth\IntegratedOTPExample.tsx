import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SecureInAppOTP from '../../components/SecureInAppOTP';
import CustomButton from '../../components/CustomButton';
import CustomInput from '../../components/CustomInput';

// This is an example of how to integrate the OTP system into your existing auth flow
interface IntegratedOTPExampleProps {
  navigation: any;
  route: any;
}

const IntegratedOTPExample: React.FC<IntegratedOTPExampleProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const [step, setStep] = useState<'phone' | 'otp' | 'success'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Get the purpose from navigation params (login, registration, etc.)
  const { purpose = 'login', userType = 'dealer' } = route.params || {};

  const handlePhoneSubmit = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Error', 'Please enter your phone number');
      return;
    }

    try {
      setIsLoading(true);
      
      // Check if phone number exists in your system
      // This would be your existing API call
      const response = await fetch('https://quickloadbe.cogweel.com/check-phone-number', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });

      const data = await response.json();

      if (data.exists) {
        // Phone exists, proceed to OTP
        setUserId(data.userId || phoneNumber); // Use actual userId from your backend
        setStep('otp');
      } else {
        if (purpose === 'login') {
          Alert.alert('Error', 'Phone number not registered. Please register first.');
        } else {
          // For registration, create temporary userId
          setUserId(phoneNumber);
          setStep('otp');
        }
      }
    } catch (error) {
      console.error('Error checking phone:', error);
      Alert.alert('Error', 'Failed to verify phone number. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOTPSuccess = async (verificationData: any) => {
    try {
      setIsLoading(true);

      // Store verification data
      await AsyncStorage.setItem('otpVerification', JSON.stringify(verificationData));

      if (purpose === 'login') {
        // For login, complete the authentication
        await completeLogin(verificationData);
      } else if (purpose === 'registration') {
        // For registration, proceed to complete registration
        navigation.navigate('CompleteRegistration', {
          phoneNumber,
          userId,
          verificationData,
          userType
        });
      } else if (purpose === 'password-reset') {
        // For password reset, proceed to reset password
        navigation.navigate('ResetPassword', {
          phoneNumber,
          userId,
          verificationData
        });
      }

      setStep('success');
    } catch (error) {
      console.error('Error handling OTP success:', error);
      Alert.alert('Error', 'Failed to complete verification. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const completeLogin = async (verificationData: any) => {
    try {
      // Your existing login API call
      const response = await fetch('https://quickloadbe.cogweel.com/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber,
          otpVerified: true,
          verificationData,
          userType
        }),
      });

      const loginData = await response.json();

      if (loginData.success) {
        // Store user data and navigate to main app
        await AsyncStorage.setItem('userData', JSON.stringify(loginData.user));
        await AsyncStorage.setItem('authToken', loginData.token);
        
        navigation.reset({
          index: 0,
          routes: [{ name: 'MainApp' }],
        });
      } else {
        throw new Error(loginData.message || 'Login failed');
      }
    } catch (error) {
      throw error;
    }
  };

  const handleOTPError = (error: string) => {
    Alert.alert('Verification Failed', error);
  };

  const handleGoBack = () => {
    if (step === 'otp') {
      setStep('phone');
    } else {
      navigation.goBack();
    }
  };

  const getPurposeTitle = () => {
    switch (purpose) {
      case 'login':
        return 'Login to Your Account';
      case 'registration':
        return 'Create New Account';
      case 'password-reset':
        return 'Reset Your Password';
      default:
        return 'Verify Your Identity';
    }
  };

  const getPurposeDescription = () => {
    switch (purpose) {
      case 'login':
        return 'Enter your phone number to log in securely';
      case 'registration':
        return 'Enter your phone number to create a new account';
      case 'password-reset':
        return 'Enter your phone number to reset your password';
      default:
        return 'Enter your phone number to continue';
    }
  };

  if (step === 'success') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.successContainer}>
          <Text style={[styles.successTitle, { color: theme.colors.primary }]}>
            ✅ Verification Successful!
          </Text>
          <Text style={[styles.successText, { color: theme.colors.onSurface }]}>
            {purpose === 'login' ? 'Logging you in...' : 'Proceeding to next step...'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (step === 'otp') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <CustomButton
              title="← Back"
              onPress={handleGoBack}
              variant="text"
              style={styles.backButton}
            />
            <Text style={[styles.headerTitle, { color: theme.colors.onBackground }]}>
              Verify Phone Number
            </Text>
          </View>

          <View style={styles.phoneInfo}>
            <Text style={[styles.phoneLabel, { color: theme.colors.onSurfaceVariant }]}>
              Verifying:
            </Text>
            <Text style={[styles.phoneNumber, { color: theme.colors.primary }]}>
              {phoneNumber}
            </Text>
          </View>

          <SecureInAppOTP
            userId={userId}
            purpose={purpose}
            onVerificationSuccess={handleOTPSuccess}
            onVerificationError={handleOTPError}
            maxAttempts={3}
            otpLength={6}
          />
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          <Text style={[styles.title, { color: theme.colors.onBackground }]}>
            {getPurposeTitle()}
          </Text>

          <Text style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            {getPurposeDescription()}
          </Text>

          <CustomInput
            label="Phone Number"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            style={styles.input}
          />

          <CustomButton
            title="Continue"
            onPress={handlePhoneSubmit}
            loading={isLoading}
            disabled={!phoneNumber.trim()}
            style={styles.continueButton}
          />

          <View style={styles.infoContainer}>
            <Text style={[styles.infoText, { color: theme.colors.onSurfaceVariant }]}>
              🔒 We'll generate a secure OTP within the app - no SMS required!
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  input: {
    marginBottom: 30,
  },
  continueButton: {
    marginBottom: 30,
  },
  infoContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  phoneInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  phoneLabel: {
    fontSize: 16,
    marginRight: 10,
  },
  phoneNumber: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  successText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default IntegratedOTPExample;
