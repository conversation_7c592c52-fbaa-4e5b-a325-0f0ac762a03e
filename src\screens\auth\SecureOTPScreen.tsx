import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SecureInAppOTP from '../../components/SecureInAppOTP';
import CustomButton from '../../components/CustomButton';
import CustomInput from '../../components/CustomInput';

interface SecureOTPScreenProps {
  navigation: any;
  route: any;
}

const SecureOTPScreen: React.FC<SecureOTPScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const [userId, setUserId] = useState('');
  const [purpose, setPurpose] = useState<'login' | 'registration' | 'password-reset' | 'transaction'>('login');
  const [showOTP, setShowOTP] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get parameters from navigation if available
  const { 
    initialUserId = '', 
    initialPurpose = 'login',
    autoStart = false 
  } = route.params || {};

  React.useEffect(() => {
    if (initialUserId) {
      setUserId(initialUserId);
    }
    if (initialPurpose) {
      setPurpose(initialPurpose);
    }
    if (autoStart && initialUserId) {
      setShowOTP(true);
    }
  }, [initialUserId, initialPurpose, autoStart]);

  const handleStartOTPProcess = () => {
    if (!userId.trim()) {
      Alert.alert('Error', 'Please enter a User ID');
      return;
    }
    setShowOTP(true);
  };

  const handleVerificationSuccess = async (data: any) => {
    try {
      setIsLoading(true);
      
      // Store verification result
      await AsyncStorage.setItem('lastOTPVerification', JSON.stringify({
        ...data,
        timestamp: new Date().toISOString()
      }));

      Alert.alert(
        'Success',
        'OTP verified successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate based on purpose
              switch (purpose) {
                case 'login':
                  navigation.replace('MainApp');
                  break;
                case 'registration':
                  navigation.navigate('CompleteRegistration', { verificationData: data });
                  break;
                case 'password-reset':
                  navigation.navigate('ResetPassword', { verificationData: data });
                  break;
                case 'transaction':
                  navigation.goBack();
                  break;
                default:
                  navigation.goBack();
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error handling verification success:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationError = (error: string) => {
    Alert.alert('Verification Failed', error);
  };

  const handleGoBack = () => {
    setShowOTP(false);
  };

  const getPurposeDescription = () => {
    switch (purpose) {
      case 'login':
        return 'Verify your identity to log in';
      case 'registration':
        return 'Verify your identity to complete registration';
      case 'password-reset':
        return 'Verify your identity to reset password';
      case 'transaction':
        return 'Verify your identity to complete transaction';
      default:
        return 'Verify your identity';
    }
  };

  if (showOTP) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <CustomButton
              title="← Back"
              onPress={handleGoBack}
              variant="text"
              style={styles.backButton}
            />
            <Text style={[styles.headerTitle, { color: theme.colors.onBackground }]}>
              Secure Verification
            </Text>
          </View>

          <View style={styles.content}>
            <Text style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
              {getPurposeDescription()}
            </Text>

            <View style={[styles.userInfo, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Text style={[styles.userInfoLabel, { color: theme.colors.onSurfaceVariant }]}>
                User ID:
              </Text>
              <Text style={[styles.userInfoValue, { color: theme.colors.onSurface }]}>
                {userId}
              </Text>
            </View>

            <SecureInAppOTP
              userId={userId}
              purpose={purpose}
              onVerificationSuccess={handleVerificationSuccess}
              onVerificationError={handleVerificationError}
              maxAttempts={3}
              otpLength={6}
            />

            <View style={styles.securityInfo}>
              <Text style={[styles.securityTitle, { color: theme.colors.primary }]}>
                🔒 Security Features
              </Text>
              <Text style={[styles.securityText, { color: theme.colors.onSurfaceVariant }]}>
                • OTP expires in 60 seconds{'\n'}
                • Device fingerprinting enabled{'\n'}
                • Rate limiting protection{'\n'}
                • Secure in-app generation{'\n'}
                • No external communication required
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.setupContainer}>
          <Text style={[styles.title, { color: theme.colors.onBackground }]}>
            Secure OTP Verification
          </Text>

          <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            Enter your details to start the secure verification process
          </Text>

          <CustomInput
            label="User ID"
            value={userId}
            onChangeText={setUserId}
            placeholder="Enter your user ID"
            style={styles.input}
          />

          <View style={styles.purposeContainer}>
            <Text style={[styles.purposeLabel, { color: theme.colors.onSurface }]}>
              Verification Purpose:
            </Text>
            {(['login', 'registration', 'password-reset', 'transaction'] as const).map((p) => (
              <CustomButton
                key={p}
                title={p.charAt(0).toUpperCase() + p.slice(1).replace('-', ' ')}
                onPress={() => setPurpose(p)}
                variant={purpose === p ? 'contained' : 'outlined'}
                style={styles.purposeButton}
              />
            ))}
          </View>

          <CustomButton
            title="Start Verification"
            onPress={handleStartOTPProcess}
            loading={isLoading}
            disabled={!userId.trim()}
            style={styles.startButton}
          />

          <View style={styles.infoContainer}>
            <Text style={[styles.infoTitle, { color: theme.colors.primary }]}>
              How it works:
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.onSurfaceVariant }]}>
              1. A secure OTP is generated within the app{'\n'}
              2. The OTP is displayed on your screen{'\n'}
              3. Enter the displayed OTP to verify{'\n'}
              4. No SMS or email required!
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  userInfo: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  userInfoValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  securityInfo: {
    marginTop: 30,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  securityText: {
    fontSize: 14,
    lineHeight: 20,
  },
  setupContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  input: {
    marginBottom: 20,
  },
  purposeContainer: {
    marginBottom: 30,
  },
  purposeLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
  },
  purposeButton: {
    marginBottom: 8,
  },
  startButton: {
    marginBottom: 30,
  },
  infoContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default SecureOTPScreen;
